package com.babeltime.service.impl;

import com.babeltime.bean.EventBean;
import com.babeltime.mapper.EventMapper;
import com.babeltime.service.EventService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class EventServiceImpl extends ServiceImpl<EventMapper, EventBean> implements EventService {

    @Autowired
    private EventMapper mapper;

    @Override
    public List<EventBean> getEvents(List<String> gnList, String formattedDateTime, List<String> ips) {
        return mapper.selectEvent(gnList, formattedDateTime, ips);
    }

    @Override
    public List<EventBean> getAdditionalEvent(List<String> gnList) {
        return mapper.selectAdditionalEvent(gnList);
    }

    @Override
    public List<EventBean> getTakesampleEvent(List<String> gnList) {
        return mapper.selectTakesampleEvent(gnList);
    }

    @Override
    public List<EventBean> getVpnDenyEvent(List<String> gnList) {
        return mapper.selectVpnDenyEvent(gnList);
    }
}
