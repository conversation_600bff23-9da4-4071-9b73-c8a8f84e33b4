<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.babeltime.mapper.EventMapper">

    <select id="selectEvent" resultType="com.babeltime.bean.EventBean">
        SELECT pid, uuid, gn
        FROM (
        SELECT pid, uuid, gn
        FROM ad_hoc.t_event_all
        WHERE gn GLOBAL IN
        <foreach item="gnItem" collection="gnList" open="(" separator="," close=")">
            #{gnItem}
        </foreach>
        AND toDateTime(ds) &lt;= #{ds}
        AND event_name = 'register'
        AND (ip GLOBAL IN
        <foreach item="item" index="index" collection="ips"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        OR ip LIKE '192.168%' OR  lower(pl) like '%tishen%')
        GROUP BY pid, uuid, gn
        UNION ALL
        SELECT pid, uuid, gn
        FROM ad_hoc.t_event_all
        WHERE gn GLOBAL IN
        <foreach item="gnItem" collection="gnList" open="(" separator="," close=")">
            #{gnItem}
        </foreach>
        AND toDateTime(ds) &lt;= #{ds}
        AND event_name = 'role_create'
        AND (ip GLOBAL IN
        <foreach item="item" index="index" collection="ips"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        OR ip LIKE '192.168%' OR  lower(pl) like '%tishen%')
        GROUP BY pid, uuid, gn
        UNION ALL
        SELECT pid, uuid, gn
        FROM ad_hoc.t_event_all
        WHERE gn GLOBAL IN
        <foreach item="gnItem" collection="gnList" open="(" separator="," close=")">
            #{gnItem}
        </foreach>
        AND toDateTime(ds) &lt;= #{ds}
        AND event_name IN ('role_login', 'role_logout')
        AND (ip GLOBAL IN
        <foreach item="item" index="index" collection="ips"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        OR ip LIKE '192.168%' OR  lower(pl) like '%tishen%')
        GROUP BY pid, uuid, gn
        ) t
        GROUP BY pid, uuid, gn
    </select>

    <select id="selectAdditionalEvent" resultType="com.babeltime.bean.EventBean">
        SELECT a.uuid,a.pid,a.gn
        FROM(
        SELECT b.pid, a.uuid, a.gn
        FROM (
        SELECT uuid, gn
        FROM ad_hoc.t_event_all
        WHERE gn GLOBAL IN
        <foreach item="gnItem" collection="gnList" open="(" separator="," close=")">
            #{gnItem}
        </foreach>
        AND log_type = 'takesample'
        AND event_name = 'FirstStartUp'
        AND sk1 = ''
        and visitParamExtractString(sk10,'Env') &lt;&gt; 'release'
        GROUP BY uuid, gn
        ) a
        LEFT JOIN (
        SELECT uuid, pid, gn
        FROM ad_hoc.t_event_all
        WHERE gn GLOBAL IN
        <foreach item="gnItem" collection="gnList" open="(" separator="," close=")">
            #{gnItem}
        </foreach>
        AND event_name = 'register'
        GROUP BY uuid, pid, gn
        ) b ON a.uuid = b.uuid AND a.gn = b.gn
        )a LEFT JOIN (
        SELECT pid,gn
        from ad_hoc.t_event_all
        WHERE gn GLOBAL IN
        <foreach item="gnItem" collection="gnList" open="(" separator="," close=")">
            #{gnItem}
        </foreach>
        AND event_name = 'order'
        group by pid,gn
        )b on a.pid=b.pid and a.gn=b.gn
        where b.pid =''
    </select>

    <select id="selectTakesampleEvent" resultType="com.babeltime.bean.EventBean">
        SELECT pid, uuid, gn
        FROM data_warehouse.takesample_all
        WHERE gn GLOBAL IN
        <foreach item="gnItem" collection="gnList" open="(" separator="," close=")">
            #{gnItem}
        </foreach>
        AND type IN ('web_login_register_deny')
        GROUP BY pid, uuid, gn
    </select>

    <select id="selectVpnDenyEvent" resultType="com.babeltime.bean.EventBean">
        SELECT t.pid, t.uuid, t.gn
        FROM (
            SELECT pid, uuid, gn
            FROM data_warehouse.takesample_all
            WHERE gn GLOBAL IN
            <foreach item="gnItem" collection="gnList" open="(" separator="," close=")">
                #{gnItem}
            </foreach>
            AND type = 'web_login_register_usevpn_deny'
            GROUP BY pid, uuid, gn
        ) t
        LEFT JOIN (
            SELECT pid, gn
            FROM data_warehouse.role_info
            WHERE gn GLOBAL IN
            <foreach item="gnItem" collection="gnList" open="(" separator="," close=")">
                #{gnItem}
            </foreach>
            GROUP BY pid, gn
        ) r ON t.pid = r.pid AND t.gn = r.gn
        WHERE r.pid IS NULL
        GROUP BY t.pid, t.uuid, t.gn
    </select>
</mapper>
