package com.babeltime.utils;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

public class LoadConfigUtil {

    public static Properties load() {
        try(InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream("config.properties")) {
            Properties prop = new Properties();
            prop.load(in);
            return prop;
        } catch (IOException e) {
           throw new RuntimeException("加载配置文件失败: ",e);
        }
    }
}
