package com.babeltime.mapper;

import com.babeltime.bean.EventBean;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface EventMapper extends BaseMapper<EventBean> {

    List<EventBean> selectEvent(@Param("gnList") List<String> gnList, @Param("ds")  String ds, @Param("ips") List<String> ips);

    List<EventBean> selectAdditionalEvent(@Param("gnList") List<String> gnList);

    List<EventBean> selectTakesampleEvent(@Param("gnList") List<String> gnList);
}
