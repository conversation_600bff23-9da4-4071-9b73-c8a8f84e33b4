package com.babeltime.controller;

import com.alibaba.excel.EasyExcel;
import com.babeltime.bean.EventBean;
import com.babeltime.bean.ExcelData;
import com.babeltime.service.EventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@RestController
public class ReadExcelDataTask {

    @Autowired
    private EventService eventService;

    private List<ExcelData> readCsvFile() {
        List<ExcelData> dataList = new ArrayList<>();
        Resource resource = new ClassPathResource("20号设备明细.csv");

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {
            String line = reader.readLine();

            while ((line = reader.readLine()) != null) {
                String[] values = line.split(",");
                if (values.length == 2) {
                    String pid = values[0].replace("\"", "").trim();
                    String bind = values[1].replace("\"", "").trim();
                    dataList.add(new ExcelData(pid, bind));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dataList;
    }

    @Scheduled(initialDelay = 300000, fixedDelay = Long.MAX_VALUE)
    public void writeTo() {
        List<ExcelData> excelData = readCsvFile();
        List<EventBean> events = new ArrayList<>();
        for (ExcelData excelDatum : excelData) {
            EventBean mg = EventBean.builder().pid(excelDatum.getPid()).uuid(excelDatum.getBind()).gn("dt").type(1).build();
            events.add(mg);
        }
        eventService.saveBatch(events);
    }
}
