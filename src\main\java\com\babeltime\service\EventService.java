package com.babeltime.service;

import com.babeltime.bean.EventBean;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;

public interface EventService extends IService<EventBean> {

    List<EventBean> getEvents(List<String> gnList, String formattedDateTime, List<String> ips);

    List<EventBean> getAdditionalEvent(List<String> gnList);

    List<EventBean> getTakesampleEvent(List<String> gnList);

    List<EventBean> getVpnDenyEvent(List<String> gnList);
}
