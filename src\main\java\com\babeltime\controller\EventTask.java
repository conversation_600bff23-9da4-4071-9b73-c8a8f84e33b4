package com.babeltime.controller;

import com.babeltime.bean.EventBean;
import com.babeltime.service.EventService;
import com.babeltime.utils.LoadConfigUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;

@RestController
public class EventTask {

    private static final Properties prop = LoadConfigUtil.load();

    @Autowired
    private EventService eventService;

    @Scheduled(cron = "0 */3 * * * ?")
    public void insertEvent() {
        String ips = (String) prop.get("ips");
        List<String> ipsList = Arrays.asList(ips.split(","));
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedDateTime = now.format(formatter);
        String gns = (String) prop.get("gns");
        List<String> gnsList = Arrays.asList(gns.split(","));
        List<EventBean> events = eventService.getEvents(gnsList, formattedDateTime, ipsList);
        eventService.saveBatch(events);
        List<EventBean> additionalEvent = eventService.getAdditionalEvent(gnsList);
        eventService.saveBatch(additionalEvent);
        List<EventBean> takesampleEvent = eventService.getTakesampleEvent(gnsList);
        eventService.saveBatch(takesampleEvent);
        List<EventBean> vpnDenyEvent = eventService.getVpnDenyEvent(gnsList);
        eventService.saveBatch(vpnDenyEvent);
    }
}
